package main

import (
	"fmt"
	"strings"

	"github.com/lxn/walk"
	. "github.com/lxn/walk/declarative"
)

func main() {
	var inputEdit *walk.LineEdit
	var listBox *walk.ListBox
	var items []string = []string{"示例项目1", "示例项目2", "示例项目3"}

	// 更新列表的函数
	updateList := func() {
		listBox.SetModel(items)
	}

	// 添加项目的函数
	addItem := func() {
		text := inputEdit.Text()
		if strings.TrimSpace(text) == "" {
			walk.MsgBox(nil, "提示", "请输入内容！", walk.MsgBoxIconInformation)
			return
		}
		items = append(items, text)
		updateList()
		inputEdit.SetText("")
		walk.MsgBox(nil, "成功", fmt.Sprintf("已添加: %s", text), walk.MsgBoxIconInformation)
	}

	// 清空列表的函数
	clearList := func() {
		result := walk.MsgBox(nil, "确认", "确定要清空列表吗？", walk.MsgBoxYesNo|walk.MsgBoxIconQuestion)
		if result == walk.DlgCmdYes {
			items = items[:0]
			updateList()
			walk.MsgBox(nil, "提示", "列表已清空！", walk.MsgBoxIconInformation)
		}
	}

	// 创建主窗口
	_, err := MainWindow{
		Title:  "Go Walk UI 应用程序",
		Size:   Size{Width: 450, Height: 400},
		Layout: VBox{Margins: Margins{10, 10, 10, 10}},
		Children: []Widget{
			Label{
				Text: "请输入内容:",
			},
			LineEdit{
				AssignTo: &inputEdit,
			},
			VSpacer{Size: 10},
			Label{
				Text: "列表内容:",
			},
			ListBox{
				AssignTo: &listBox,
				Model:    items,
				MinSize:  Size{Height: 200},
			},
			VSpacer{Size: 10},
			Composite{
				Layout: HBox{},
				Children: []Widget{
					HSpacer{},
					PushButton{
						Text:    "提交",
						MinSize: Size{Width: 80},
						OnClicked: func() {
							addItem()
						},
					},
					PushButton{
						Text:    "清空列表",
						MinSize: Size{Width: 80},
						OnClicked: func() {
							clearList()
						},
					},
					HSpacer{},
				},
			},
		},
	}.Run()

	if err != nil {
		walk.MsgBox(nil, "错误", fmt.Sprintf("创建窗口失败: %v", err), walk.MsgBoxIconError)
	}
}
