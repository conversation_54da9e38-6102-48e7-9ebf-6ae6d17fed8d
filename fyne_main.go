package main

import (
	"fmt"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

type MyApp struct {
	inputEntry *widget.Entry
	listWidget *widget.List
	items      []string
}

func (a *MyApp) updateList() {
	a.listWidget.Refresh()
}

func (a *MyApp) addItem(text string) {
	a.items = append(a.items, text)
	a.updateList()
}

func (a *MyApp) clearItems() {
	a.items = a.items[:0]
	a.updateList()
}

func main() {
	// 创建应用程序
	myApp := app.New()
	myApp.SetIcon(nil)
	
	// 创建主窗口
	myWindow := myApp.NewWindow("Go Fyne UI 应用程序")
	myWindow.Resize(fyne.NewSize(450, 400))

	// 创建应用程序实例
	appInstance := &MyApp{
		items: []string{"示例项目1", "示例项目2", "示例项目3"},
	}

	// 创建输入框
	appInstance.inputEntry = widget.NewEntry()
	appInstance.inputEntry.SetPlaceHolder("请输入内容...")

	// 创建列表
	appInstance.listWidget = widget.NewList(
		func() int {
			return len(appInstance.items)
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("模板")
		},
		func(i widget.ListItemID, o fyne.CanvasObject) {
			o.(*widget.Label).SetText(appInstance.items[i])
		},
	)

	// 创建提交按钮
	submitBtn := widget.NewButton("提交", func() {
		text := strings.TrimSpace(appInstance.inputEntry.Text)
		if text == "" {
			dialog.ShowInformation("提示", "请输入内容！", myWindow)
			return
		}

		// 添加到列表
		appInstance.addItem(text)
		
		// 清空输入框
		appInstance.inputEntry.SetText("")
		
		// 显示成功消息
		dialog.ShowInformation("成功", fmt.Sprintf("已添加: %s", text), myWindow)
	})

	// 创建清空按钮
	clearBtn := widget.NewButton("清空列表", func() {
		dialog.ShowConfirm("确认", "确定要清空列表吗？", func(confirmed bool) {
			if confirmed {
				appInstance.clearItems()
				dialog.ShowInformation("提示", "列表已清空！", myWindow)
			}
		}, myWindow)
	})

	// 创建布局
	content := container.NewVBox(
		widget.NewLabel("请输入内容:"),
		appInstance.inputEntry,
		widget.NewSeparator(),
		widget.NewLabel("列表内容:"),
		container.NewBorder(nil, nil, nil, nil, appInstance.listWidget),
		widget.NewSeparator(),
		container.NewHBox(
			widget.NewButton("", func() {}), // 占位符，用于居中
			submitBtn,
			clearBtn,
			widget.NewButton("", func() {}), // 占位符，用于居中
		),
	)

	// 设置窗口内容
	myWindow.SetContent(content)

	// 显示窗口并运行
	myWindow.ShowAndRun()
}
