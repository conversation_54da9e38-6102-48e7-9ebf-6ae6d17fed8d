package main

import (
	"fmt"
	"strings"

	"github.com/lxn/walk"
	. "github.com/lxn/walk/declarative"
)

type MyMainWindow struct {
	*walk.MainWindow
	inputEdit *walk.LineEdit
	listBox   *walk.ListBox
	submitBtn *walk.PushButton
	items     []string
}

func (mw *MyMainWindow) updateListBox() {
	mw.listBox.SetModel(mw.items)
}

func (mw *MyMainWindow) addItem(item string) {
	mw.items = append(mw.items, item)
	mw.updateListBox()
}

func (mw *MyMainWindow) clearItems() {
	mw.items = mw.items[:0]
	mw.updateListBox()
}

func main() {
	var mw MyMainWindow

	// 初始化数据
	mw.items = []string{"示例项目1", "示例项目2", "示例项目3"}

	// 创建主窗口
	err := MainWindow{
		AssignTo: &mw.MainWindow,
		Title:    "Go Walk UI 应用程序",
		Size:     Size{Width: 450, Height: 400},
		Layout:   VBox{Margins: Margins{10, 10, 10, 10}},
		Children: []Widget{
			Label{
				Text: "请输入内容:",
			},
			LineEdit{
				AssignTo: &mw.inputEdit,
			},
			VSpacer{Size: 10},
			Label{
				Text: "列表内容:",
			},
			ListBox{
				AssignTo: &mw.listBox,
				Model:    mw.items,
				MinSize:  Size{Height: 200},
			},
			VSpacer{Size: 10},
			Composite{
				Layout: HBox{},
				Children: []Widget{
					HSpacer{},
					PushButton{
						AssignTo: &mw.submitBtn,
						Text:     "提交",
						MinSize:  Size{Width: 80},
						OnClicked: func() {
							mw.onSubmitClicked()
						},
					},
					PushButton{
						Text:    "清空列表",
						MinSize: Size{Width: 80},
						OnClicked: func() {
							mw.onClearClicked()
						},
					},
					HSpacer{},
				},
			},
		},
	}.Create()

	if err != nil {
		walk.MsgBox(nil, "错误", fmt.Sprintf("创建窗口失败: %v", err), walk.MsgBoxIconError)
		return
	}

	// 运行应用程序
	mw.Run()
}

func (mw *MyMainWindow) onSubmitClicked() {
	text := mw.inputEdit.Text()
	if strings.TrimSpace(text) == "" {
		walk.MsgBox(mw, "提示", "请输入内容！", walk.MsgBoxIconInformation)
		return
	}

	// 添加到列表
	mw.addItem(text)

	// 清空输入框
	mw.inputEdit.SetText("")

	// 显示成功消息
	walk.MsgBox(mw, "成功", fmt.Sprintf("已添加: %s", text), walk.MsgBoxIconInformation)
}

func (mw *MyMainWindow) onClearClicked() {
	result := walk.MsgBox(mw, "确认", "确定要清空列表吗？", walk.MsgBoxYesNo|walk.MsgBoxIconQuestion)
	if result == walk.DlgCmdYes {
		mw.clearItems()
		walk.MsgBox(mw, "提示", "列表已清空！", walk.MsgBoxIconInformation)
	}
}
