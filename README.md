# Go Walk UI 应用程序

这是一个使用Go语言和Walk库创建的简单Windows桌面应用程序。

## 功能特性

- **输入框**: 用户可以输入文本内容
- **列表框**: 显示已添加的项目列表
- **提交按钮**: 将输入框中的内容添加到列表中
- **清空列表按钮**: 清空列表中的所有项目
- **消息提示**: 提供用户友好的操作反馈

## 文件说明

- `main.go` - 主程序源代码
- `build.bat` - Windows批处理编译脚本
- `walk-ui-app.exe` - 编译后的可执行文件
- `go.mod` - Go模块配置文件

## 如何运行

### 方法1: 直接运行exe文件
双击 `walk-ui-app.exe` 即可运行应用程序。

### 方法2: 从源代码运行
```bash
go run main.go
```

### 方法3: 使用批处理脚本编译
双击 `build.bat` 文件，会自动编译生成新的exe文件。

## 如何使用

1. 启动应用程序后，会看到一个包含输入框、列表框和按钮的窗口
2. 在输入框中输入任何文本内容
3. 点击"提交"按钮，输入的内容会被添加到下方的列表中
4. 点击"清空列表"按钮可以清空列表中的所有项目
5. 程序会显示相应的提示消息

## 技术栈

- **Go语言**: 主要编程语言
- **Walk库**: Windows GUI框架 (github.com/lxn/walk)
- **Windows API**: 底层系统调用

## 编译选项说明

使用 `-ldflags="-H windowsgui"` 参数编译，这样生成的exe文件运行时不会显示控制台窗口，提供更好的用户体验。

## 系统要求

- Windows操作系统
- 无需额外安装Go运行时（exe文件已包含所有依赖）
